/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-modal {
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;
}

.close-modal:hover {
    color: #333;
}


.reply-input-container {
    position: relative;
}

.reply-input-container .submit-btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
}

/* static/css/styles.css */
/* 全局基础样式 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* 表单元素 */
input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 10px 0;
}

textarea {
    width: 97%;
    height: 120px;
    font-size: 14px;
    line-height: 1.6;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
}

/* 编辑回复表单专属样式 */
.edit-reply-textarea {
    font-size: 13px;
    line-height: 1.7;
    min-height: 180px;
}

/* 头部操作栏 */
.action-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
}

.header-actions {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

/* 容器公共样式 */
.container {
    width: 95%;
    max-width: 800px;
    margin: 20px auto;
    padding: 15px;
}

@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .container {
        width: 92%;
        margin: 10px auto;
        padding: 10px;
    }

    .category-nav {
        margin: 1rem 0;
    }

    .category-tags {
        flex-direction: column;
        gap: 0.3rem;
    }

    .category-tags .submit-btn {
        width: 100%;
        text-align: center;
        margin-left: 0 !important;
    }

    .tag-item {
        padding: 0.4rem 0.8rem;
    }

    .post-title {
        font-size: 1rem;
    }

    .post-excerpt {
        font-size: 0.9rem;
    }

    .action-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .action-header h1 {
        font-size: 1.5rem;
        text-align: center;
    }

    .submit-btn {
        width: 100%;
        text-align: center;
    }

    .time-info {
        flex-direction: column;
        gap: 8px;
    }

    .action-buttons {
        width: 100%;
        justify-content: flex-start;
        gap: 10px;
    }

    .btn-edit,
    .btn-delete {
        padding: 6px 12px;
        font-size: 14px;
    }

    .modal-content {
        width: 90%;
        margin: 20% auto;
        padding: 15px;
    }

    .floating-reply-btn {
        padding: 12px 20px;
        font-size: 14px;
        right: 15px;
        bottom: 15px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }

    .page-link {
        padding: 6px 12px;
        font-size: 14px;
    }

    .reply-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .sort-controls {
        width: 100%;
    }

    .sort-toggle {
        width: 100%;
        padding: 8px;
    }
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-edit {
    padding: 4px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.85em;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    background: #28a745;
    color: white !important;
}

.btn-delete {
    padding: 4px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.85em;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    background: #dc3545;
    color: white !important;
}

.submit-btn {
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85em;
    transition: all 0.2s;
}

/* 分页控件 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.page-link {
    padding: 5px 10px;
    margin: 0 5px;
    text-decoration: none;
    color: #007bff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.page-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.page-link:hover {
    background-color: #00bbff;
}

/* 时间信息 */
.time-info {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 1.5rem;
}

.new-post-btn {
    background: #007bff;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    transition: all 0.2s;
}

.new-post-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.title-card {
    background: #eee;
    border-bottom: 1px solid #eee;
    padding: 10px 12px;
    margin: 10px 0;
}


.back-link {
    background: #007bff;
    color: white !important;
    padding: 12px 24px;
    border-radius: 50px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 999;
    text-decoration: none;
    font-size: 0.95rem;
}

.back-link:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}


/* 分类导航样式 */
.category-nav {
    margin: 1.5rem 0;
    padding: 0.5rem 0;
}

.category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.6rem;
    align-items: center;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1.2rem;
    border-radius: 25px;
    background: linear-gradient(145deg, #f0f2f5, #e1e5ea);
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid #e4e6eb;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.tag-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tag-item:hover {
    background: linear-gradient(145deg, #e4e6eb, #d1d5da);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.tag-item:hover::before {
    opacity: 1;
}

.tag-item.active {
    background: linear-gradient(145deg, #007bff, #006fe6);
    color: white;
    border-color: #0062cc;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    font-weight: 600;
}

.tag-item.active::before {
    background: linear-gradient(145deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
}

.tag-count {
    margin-left: 0.6rem;
    padding: 0.2rem 0.6rem;
    background: rgba(0,0,0,0.08);
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
}

.tag-item.active .tag-count {
    background: rgba(255,255,255,0.25);
}

.submit-btn {
    background: linear-gradient(145deg, #28a745, #218838);
    color: white;
    padding: 0.5rem 1.2rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(40,167,69,0.3);
    font-weight: 500;
    margin-left: auto;
}

.submit-btn:hover {
    background: linear-gradient(145deg, #218838, #1e7e34);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40,167,69,0.4);
}

@media (max-width: 768px) {
    .category-tags {
        gap: 0.4rem;
        padding: 0.5rem 0;
    }
    
    .tag-item {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }
    
    .tag-count {
        padding: 0.15rem 0.5rem;
        font-size: 0.75em;
    }
    
    .submit-btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }
}



/* 帖子卡片 */
.post-card {
    background: #f9f7f7;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #eee;
    position: relative;
}

/* 帖子内容样式 */
.post-content-text {
    line-height: 1.2;
    color: #2c3e50;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1.0rem;
    font-weight: bold;
    margin: 1.5rem 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.post-content-text p {
    margin: 1.2em 0;
}

@media (max-width: 768px) {
    .post-content-text {
        font-size: 1rem;
        line-height: 1.7;
        padding-left: 1rem;
        border-left: 3px solid #e3e8f0;
        margin: 1.2rem 0;
    }
}

.reply-content {
    line-height: 1.5;
    color: #2c3e50;
    margin: 1.2em 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1.0rem;
    letter-spacing: 0.01em;
    padding: 0.5em 0;
    text-align: justify;
}

.post-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.post-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.post-sort {
    display: flex;
    gap: 10px;
}

.post-title {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.4;
}

.post-title a {
    color: #1a1a1a;
    text-decoration: none;
    transition: color 0.2s;
}

.post-title a:hover {
    color: #007bff;
}

.comment-count {
    color: #28a745;
    font-size: 0.9em;
    margin-left: 0.5rem;
}

.post-time {
    color: #6c757d;
    font-size: 0.9em;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 内容摘要 */
.post-excerpt {
    color: #4a4a4a;
    margin: 0;
    font-size: 0.95em;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 元信息 */
.post-meta {
    display: flex;
    gap: 1.2rem;
    font-size: 0.85em;
    color: #6c757d;
    align-items: center;
}

.post-meta i {
    margin-right: 0.3rem;
    width: 1em;
    color: #999;
}

.author { color: #007bff; }
.views { color: #6c757d; }
/* 新建帖子表单样式 */
.post-form {
    background: #ffffff;
    padding: 1.5rem; /* 减小 padding */
    border-radius: 10px; /* 稍微减小圆角 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); /* 更轻量的阴影 */
    margin-top: 0.75rem; /* 减小外边距 */
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.form-field {
    margin-bottom: 0.75rem; /* 减小字段间的间距 */
}

.form-field label {
    display: block;
    margin-bottom: 0.3rem; /* 减小标签与输入框之间的间距 */
    color: #333;
    font-weight: 500;
    font-size: 0.95rem; /* 稍微减小字体大小 */
}

.form-field input,
.form-field textarea {
    width: 100%;
    max-width: 100%;
    padding: 0.6rem 0.8rem; /* 减小内边距 */
    border: 1.5px solid #e0e0e0; /* 稍微减小边框厚度 */
    border-radius: 6px; /* 稍微减小圆角 */
    font-size: 0.95rem; /* 稍微减小字体大小 */
    transition: all 0.3s ease;
    background: #f8f9fa;
    box-sizing: border-box;
}

.form-field input:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #007bff;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-field textarea {
    resize: vertical;
    min-height: 150px;
}

.form-field input::placeholder,
.form-field textarea::placeholder {
    color: #adb5bd;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .post-form {
        padding: 1.5rem;
    }

    .form-field input,
    .form-field textarea {
        padding: 0.7rem;
    }
}

.post-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.post-title {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.4;
}

.post-title a {
    color: #1a1a1a;
    text-decoration: none;
    transition: color 0.2s;
}

.post-title a:hover {
    color: #007bff;
}

.comment-count {
    color: #28a745;
    font-size: 0.9em;
    margin-left: 0.5rem;
}

.post-time {
    color: #6c757d;
    font-size: 0.9em;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 内容摘要 */
.post-excerpt {
    color: #4a4a4a;
    margin: 0;
    font-size: 0.95em;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 元信息 */
.post-meta {
    display: flex;
    gap: 1.2rem;
    font-size: 0.85em;
    color: #6c757d;
    align-items: center;
}

/* 排序控制 */
.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1.5rem 0;
    padding: 0.5rem 0;
    border-bottom: 2px solid #f0f2f5;
}

.reply-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #343a40;
    font-weight: 600;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.9em;
}

.sort-controls span {
    color: #6c757d;
}

.sort-link {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s;
}

.sort-link:hover {
    background-color: #f0f2f5;
}

.sort-link.active {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

/* 新增切换按钮样式 */
.sort-toggle {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.sort-toggle:hover {
    background: #e9ecef;
    border-color: #ced4da;
    color: #212529;
}

.sort-toggle.active {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.post-meta i {
    margin-right: 0.3rem;
    width: 1em;
    color: #999;
}

.author { color: #007bff; }
.views { color: #6c757d; }
.floating-reply-btn {
    padding: 4px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.85em;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    background-color: #007bff;
    color: white;
}

.floating-reply-btn:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .floating-reply-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}

.post-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.post-sort {
    display: flex;
    gap: 10px;
}

.post-title {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.4;
}

.post-title a {
    color: #1a1a1a;
    text-decoration: none;
    transition: color 0.2s;
}

.post-title a:hover {
    color: #007bff;
}

.comment-count {
    color: #28a745;
    font-size: 0.9em;
    margin-left: 0.5rem;
}

.post-time {
    color: #6c757d;
    font-size: 0.9em;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 内容摘要 */
.post-excerpt {
    color: #4a4a4a;
    margin: 0;
    font-size: 0.95em;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 元信息 */
.post-meta {
    display: flex;
    gap: 1.2rem;
    font-size: 0.85em;
    color: #6c757d;
    align-items: center;
}

.post-meta i {
    margin-right: 0.3rem;
    width: 1em;
    color: #999;
}

.author { color: #007bff; }
.views { color: #6c757d; }
/* 固定导航样式 */
.fixed-nav {
    position: sticky;
    top: 0;
    background: #fff;
    padding: 10px 0;
    z-index: 998;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nav-toggle {
    display: none;
    cursor: pointer;
    text-align: center;
    padding: 5px;
    background-color: #f0f2f5;
    border-radius: 4px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

@media (max-width: 768px) {
    .fixed-nav {
        padding: 5px 0;
        background-color: rgba(255, 255, 255, 0.9);
        transition: transform 0.3s ease;
    }
    
    .nav-toggle {
        display: block;
        margin: 0 auto 5px;
        width: 90%;
    }
    
    .fixed-nav .category-tags {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 3px;
        -webkit-overflow-scrolling: touch;
        flex-direction: row;
    }

    .fixed-nav .tag-item {
        flex: 0 0 auto;
        white-space: nowrap;
        padding: 0.3rem 0.8rem;
        font-size: 0.9rem;
    }
}

.post-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.post-sort {
    display: flex;
    gap: 10px;
}

.post-title {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.4;
}

.post-title a {
    color: #1a1a1a;
    text-decoration: none;
    transition: color 0.2s;
}

.post-title a:hover {
    color: #007bff;
}

.comment-count {
    color: #28a745;
    font-size: 0.9em;
    margin-left: 0.5rem;
}

.post-time {
    color: #6c757d;
    font-size: 0.9em;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 内容摘要 */
.post-excerpt {
    color: #4a4a4a;
    margin: 0;
    font-size: 0.95em;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 元信息 */
.post-meta {
    display: flex;
    gap: 1.2rem;
    font-size: 0.85em;
    color: #6c757d;
    align-items: center;
}

.post-meta i {
    margin-right: 0.3rem;
    width: 1em;
    color: #999;
}

.author { color: #007bff; }
.views { color: #6c757d; }
.floating-reply-btn {
    padding: 4px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.85em;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    background-color: #007bff;
    color: white;
}

.floating-reply-btn:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .floating-reply-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}

.post-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.post-sort {
    display: flex;
    gap: 10px;
}

.post-title {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.4;
}

.post-title a {
    color: #1a1a1a;
    text-decoration: none;
    transition: color 0.2s;
}

.post-title a:hover {
    color: #007bff;
}

.comment-count {
    color: #28a745;
    font-size: 0.9em;
    margin-left: 0.5rem;
}

.post-time {
    color: #6c757d;
    font-size: 0.9em;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 内容摘要 */
.post-excerpt {
    color: #4a4a4a;
    margin: 0;
    font-size: 0.95em;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 元信息 */
.post-meta {
    display: flex;
    gap: 1.2rem;
    font-size: 0.85em;
    color: #6c757d;
    align-items: center;
}

.post-meta i {
    margin-right: 0.3rem;
    width: 1em;
    color: #999;
}

.author { color: #007bff; }
.views { color: #6c757d; }
