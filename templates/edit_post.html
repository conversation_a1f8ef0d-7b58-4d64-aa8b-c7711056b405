<!DOCTYPE html>
<html>
<head>
    <title>编辑帖子</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pure/pure-min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <div class="container">
        <div class="action-header">
            <h1>编辑帖子</h1>
            <a href="{{ url_for('index') }}" class="back-link">← 返回列表</a>
        </div>
        
        <form action="{{ url_for('edit_post', post_id=post.id) }}" method="post" class="pure-form pure-form-stacked">
            <fieldset>
                <div class="form-field">
                    <label for="title">标题</label>
                    <input type="text" name="title" value="{{ post.title }}" required class="pure-input-1">
                </div>
                <div class="form-field">
                    <label for="category">分类</label>
                    <input type="text" name="category" value="{{ post.category }}" placeholder="输入分类" required class="pure-input-1">
                </div>
                <div class="form-field">
                    <label for="content">内容</label>
                    <textarea name="content" required class="pure-input-1">{{ post.content }}</textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="pure-button pure-button-primary">保存修改</button>
                </div>
            </fieldset>
        </form>
    </div>
</body>
</html>
