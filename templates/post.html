<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的论坛 - {{ post.title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script>
        // 分类导航切换函数
        function toggleCategoryNav() {
            const categoryTags = document.getElementById('categoryTags');
            const navToggleIcon = document.getElementById('navToggleIcon');
            
            if (categoryTags.style.display === 'none') {
                categoryTags.style.display = 'flex';
                navToggleIcon.textContent = '▼';
            } else {
                categoryTags.style.display = 'none';
                navToggleIcon.textContent = '▲';
            }
        }
        
        // 页面加载时初始化分类导航显示状态
        window.addEventListener('DOMContentLoaded', function() {
            // 在移动设备上默认隐藏分类标签
            if (window.innerWidth <= 768) {
                const categoryTags = document.getElementById('categoryTags');
                categoryTags.style.display = 'none';
                document.getElementById('navToggleIcon').textContent = '▲';
            }
        })
        
        // 模态框相关函数
        function showReplyModal() {
            document.getElementById('reply-modal').style.display = 'block';
        }

        function closeReplyModal() {
            document.getElementById('reply-modal').style.display = 'none';
            document.querySelector('#reply-modal form').reset();
        }

        function handleDeleteReply(event, replyId) {
        if (!confirm('确定要删除这个回复吗？')) return false;
        
        fetch(`/reply/${replyId}/delete`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => {
            if (response.ok) {
                document.querySelector(`.post-card[data-reply-id="${replyId}"]`).remove();
            } else {
                alert('删除失败');
            }
        });
        return false;
    }

    function handleDeletePost(event, postId) {
            event.preventDefault();
            if (!confirm('确定要删除这个帖子吗？')) return;

            fetch(`/post/${postId}/delete`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/';
                    } else {
                        alert(data.message);
                    }
                });
        }

        function handleReplySubmit(event, postId) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            fetch(`/reply/${postId}`, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const replyContent = formData.get('content');
                        const replyHtml = `
                    <div class="post-card" data-reply-id="${data.replyId}">
                        <div class="time-info">
                            <span>回复时间：${new Date().toLocaleString("sv-SE")}</span>
                            <div class="action-buttons">
                                <button class="btn-edit"
                                    onclick="showEditModal('${data.replyId}', document.getElementById('reply-${data.replyId}').textContent);">编辑</button>
                                <form action="/reply/${data.replyId}/delete" method="post">
                                    <button type="submit" class="btn-delete">删除</button>
                                </form>
                            </div>
                        </div>
                        <div class="reply-content" id="reply-${data.replyId}">${replyContent}</div>
                    </div>
                `;
                        document.querySelector('#replies-list').insertAdjacentHTML('afterbegin', replyHtml);
                        form.reset();
                        closeReplyModal();
                    } else {
                        alert('回复提交失败，请重试。');
                    }
                });
        }
        function toggleSortOrder() {
            const currentSort = "{{ current_sort }}";
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
            window.location.href = `{{ url_for('view_post', post_id=post.id) }}?page={{ page }}&sort=${newSort}`;
        }
    </script>
</head>

<body>
    <div class="container">
        <!-- 分类导航 -->
        <div class="category-nav fixed-nav" id="categoryNav">
            <div class="nav-toggle" onclick="toggleCategoryNav()">分类 <span id="navToggleIcon">▼</span></div>
            <div class="category-tags" id="categoryTags">
                <a href="{{ url_for('index') }}" class="tag-item {% if not post.category %}active{% endif %}">全部</a>
                {% for cat in categories %}
                <a href="{{ url_for('index', category=cat.category) }}" 
                   class="tag-item {% if post.category == cat.category %}active{% endif %}">
                    {{ cat.category }}
                    <span class="tag-count">{{ cat.count }}</span>
                </a>
                {% endfor %}
            </div>
        </div>

        <!-- 回复列表标题和排序控制 -->
        <div class="reply-header">
            <h3>{{ post.title }}（{{ total_replies }}条）</h3>
            <div class="sort-controls">
                <button class="sort-toggle" onclick="toggleSortOrder()">
                    {{ '时间倒序 ▼' if current_sort == 'desc' else '时间正序 ▲' }}
                </button>
            </div>
        </div>

        <!-- 帖子内容 -->
        <div class="post-card">
            <div class="time-info">
                <span>发布时间：{{ post.created_at | beijing_time }}</span>
                <div class="action-buttons">
                    <button class="btn-edit" onclick="showPostEditModal()">编辑</button>
                    <!-- 只在无回复时显示删除按钮 -->
                    {% if replies|length == 0 %}
                    <form onsubmit="handleDeletePost(event, '{{ post.id }}')">
                        <button type="submit" class="btn-delete">删除</button>
                    </form>
                    {% endif %}
                    <button onclick="showReplyModal()" class="floating-reply-btn">回复</button>
                </div>
            </div>
            <div class="post-content-text" id="post-content">{{ post.content|safe }}</div>
        </div>


        <!-- 回复模态框 -->
        <div id="reply-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>回复</h3>
                    <span class="close-modal" onclick="closeReplyModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form onsubmit="handleReplySubmit(event, '{{ post.id }}')" method="post">
                        <textarea name="content" placeholder="输入您的回复内容..." required></textarea>
                        <div class="form-actions">
                            <button type="submit" class="submit-btn">提交回复</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div id="replies-list">
            {% for reply in replies %}
            <div class="post-card" data-reply-id="{{ reply.id }}">
                <div class="time-info">
                    <span>回复时间：{{ reply.created_at | beijing_time }}</span>
                    <div class="action-buttons">
                        {% set edit_url = url_for('edit_reply', reply_id=reply.id) %}
                        <button class="btn-edit" data-reply-id="{{ reply.id }}">编辑</button>
                        <form onsubmit="return handleDeleteReply(event, {{ reply.id }})">
                            <button type="submit" class="btn-delete">删除</button>
                        </form>
                    </div>
                </div>
                <div class="reply-content" id="reply-{{ reply.id }}">{{ reply.content|safe }}</div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            {% if total_pages > 1 %}
            {% for p in range(1, total_pages + 1) %}
            <a href="{{ url_for('view_post', post_id=post.id, page=p) }}"
                class="page-link {% if p == page %}active{% endif %}">{{ p }}</a>
            {% endfor %}
            {% endif %}
        </div>
    </div>

    <!-- 回复编辑模态框 -->
    <div id="edit-reply-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑回复</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="edit-reply-form">
                    <textarea name="content" required></textarea>
                    <div class="form-actions">
                        <button type="submit" class="submit-btn">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 帖子编辑模态框 -->
    <div id="edit-post-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑帖子</h3>
                <span class="close-modal" onclick="closePostEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="edit-post-form">
                    <div class="form-field">
                        <label for="post-title">标题</label>
                        <input type="text" id="post-title" name="title" value="{{ post.title }}" required>
                    </div>
                    <div class="form-field">
                        <label for="post-category">分类</label>
                        <input type="text" id="post-category" name="category" value="{{ post.category }}" required>
                    </div>
                    <div class="form-field">
                        <label for="post-content">内容</label>
                        <textarea id="post-content" name="content" rows="12" required>{{ post.content }}</textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="submit-btn">保存修改</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 模态框相关函数
        const modal = document.getElementById('edit-reply-modal');
        const postModal = document.getElementById('edit-post-modal');
        document.addEventListener('click', function(event) {
            if (event.target.matches('.btn-edit') && event.target.dataset.replyId) {
                event.preventDefault();
                const replyId = event.target.dataset.replyId;
                const contentElement = document.getElementById(`reply-${replyId}`);
                if (contentElement) {
                    showEditModal(replyId, contentElement.outerHTML);
                }
            }
        });

        const closeBtn = modal.querySelector('.close-modal');
        const editForm = document.getElementById('edit-reply-form');
        const postEditForm = document.getElementById('edit-post-form');
        let currentReplyId = null;

        function showEditModal(replyId, content) {
            currentReplyId = replyId;
            const replyContentElement = document.getElementById(`reply-${replyId}`);
            const htmlSource = replyContentElement.innerHTML;
            editForm.querySelector('textarea').value = htmlSource;
            modal.style.display = 'block';
        }

        function closeModal() {
            modal.style.display = 'none';
            currentReplyId = null;
            editForm.reset();
        }

        function showPostEditModal() {
            postModal.style.display = 'block';
        }

        function closePostEditModal() {
            postModal.style.display = 'none';
            postEditForm.reset();
        }

        closeBtn.onclick = closeModal;

        editForm.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(editForm);
            try {
                const response = await fetch(`/reply/${currentReplyId}/edit`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });
                if (!response.ok) throw new Error('保存失败');
                const data = await response.json();
                
                // 更新页面中的回复内容
                const replyElement = document.querySelector(`.post-card[data-reply-id="${data.id}"]`);
                if (replyElement) {
                    replyElement.querySelector('.reply-content').innerHTML = data.content;
                    replyElement.querySelector('.time-info span').textContent = `回复时间：${data.created_at}`;
                }
                closeModal();
            } catch (error) {
                alert(error.message || '发生错误');
            }
        };

        // 帖子编辑表单提交处理
        postEditForm.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(postEditForm);
            try {
                const response = await fetch(`/post/{{ post.id }}/edit`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });
                if (!response.ok) throw new Error('保存失败');
                const data = await response.json();
                
                // 更新页面中的帖子内容
                const titleElement = document.querySelector('.post-title');
                const contentElement = document.getElementById('post-content');
                
                if (titleElement && contentElement) {
                    titleElement.textContent = data.title;
                    contentElement.textContent = data.content;
                } else {
                    console.error('元素未找到，正在重载页面...');
                    window.location.reload();
                }
                closePostEditModal();
            } catch (error) {
                alert(error.message || '发生错误');
            }
        };
    </script>
</body>

</html>