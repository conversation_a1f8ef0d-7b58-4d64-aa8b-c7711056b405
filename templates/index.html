<!DOCTYPE html>
<html>

<head>
    <title>我的论坛</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pure/pure-min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>

<body>
    <div class="container">
        <!-- 更新后的分类导航 -->
        <!-- 排序控件 -->

        <div class="category-nav">
            <div class="pure-g category-tags">
                <div class="pure-u-1 pure-u-md-3-4">
                    <div class="pure-g">
                        <div class="pure-u-1-4 pure-u-md-1-6">
                            <a href="?category=" class="tag-item {% if not selected_category %}active{% endif %}">全部</a>
                        </div>
                        {% for cat in categories %}
                        <div class="pure-u-1-4 pure-u-md-1-6">
                            <a href="?category={{ cat.category }}"
                                class="tag-item {% if selected_category == cat.category %}active{% endif %}">
                                {{ cat.category }}
                                <span class="tag-count">{{ cat.count }}</span>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="pure-u-1 pure-u-md-1-4" style="text-align: right;">
                    <a href="{{ url_for('new_post') }}" class="submit-btn">发布</a>
                </div>
            </div>
        </div>



        <!-- 帖子列表保持不变 -->
        <!-- 在帖子列表循环部分更新为 -->
        {% for post in posts %}
        <div class="post-card">
            <div class="post-content">
                <div class="post-header">
                    <h3 class="post-title">
                        <a href="{{ url_for('view_post', post_id=post.id) }}">
                            {{ post.title }}
                        </a>
                    </h3>
                    <time class="post-time">{{ post.created_at | beijing_time }}</time>
                </div>

                <!-- 新增内容摘要 -->
                {% if post.content %}
                <p class="post-excerpt">
                    {{ post.content | safe | truncate(150, true, '...') }}
                </p>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</body>

</html>
